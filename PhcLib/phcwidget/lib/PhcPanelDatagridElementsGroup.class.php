<?php
class PhcPanelDatagridElementsGroup
{
	private $rowBoundName, $name, $columns = array();
	
	public function __construct($name, $columns, $options = array ())
	{
		$this->setName($name);
		$this->setColumns($columns);
		$this->options = $options;
	}
	
	public function setName($val)
	{
		$this->name = $val;
	}
	
	public function getName()
	{
		return $this->name;
	}
	
	public function setColumns($val)
	{
		if (!is_array($val))
		{
			throw new InvalidArgumentException(sprintf('%s: Incorrect value.', get_class($this)));
		}
		$this->columns = $val;
	}
	
	public function getColumns()
	{
		return $this->columns;
	}
	
	public function addOption($name, $value = null)
	{
		$this->options[$name] = $value;
	}
	
	public function setOption($name, $value)
	{
		if (!in_array($name, array_keys($this->options)))
		{
			throw new InvalidArgumentException(sprintf('%s does not support the following option: \'%s\'.', get_class($this), $name));
		}
	
		$this->options[$name] = $value;
	}
	
	public function getOption($name)
	{
		return isset ($this->options[$name]) ? $this->options[$name] : null;
	}
	
	public function hasOption($name)
	{
		return array_key_exists($name, $this->options);
	}
	
	public function getOptions()
	{
		return $this->options;
	}
	
	public function setOptions($options)
	{
		$this->options = $options;
	}
  
	public function getClassName()
	{
		return __CLASS__;
	}
  
	public function render()
	{
	
	
	}
}

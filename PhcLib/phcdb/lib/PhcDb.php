<?php
class PhcDb
{
  public static function set_to_array($set)
  {
    return !is_null($set) ? explode(',', $set) : array ();
  }

  public static function parse_sproc_result($result)
  {
    $pattern = '#<(success|failure|warning)>(.*)</(\1)>#i';
    $matches = array ();
    $rst = array ();

    if (preg_match($pattern, $result, $matches))
    {
      $rst['result'] = utf8_encode($matches[1]);
      $rst['content'] = utf8_encode($matches[2]);
    }

    return $rst;
  }

  public static function sortByNumero($a, $b)
  {

    $a_val = $a->getNumero();
    $b_val = $b->getNumero();

    if ($a_val == $b_val)
    {
      return 0;
    }

    return ($a_val > $b_val) ? 1 : -1;
  }

  public static function prepare_sproc($sproc, $params = array())
  {
    $sproc = str_replace('?', '%s', $sproc);

   return vsprintf($sproc, $params);
  }

}

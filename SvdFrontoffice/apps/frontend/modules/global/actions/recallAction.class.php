<?php

class recallAction extends sfAction
{
    protected $formattedPhoneNumber;

    public function execute($request)
    {
        if (!$request->isXmlHttpRequest())
        {
            $this->redirect('homepage');
        }

        $this->getResponse()->setContentType('application/json');

        $response = array('subscribed' => false);

        if (!PhcDbProspect::recallIsActive())
        {
            $response['content'] = "Le rappel imm&eacute;diat est disponible du lundi au vendredi de 10h &agrave; 13h et de 14h &agrave; 19h. Veuillez r&eacute;essayer ult&eacute;rieurement.";

            return $this->response($response);
        }

        if (!$this->isValidPhoneNumber($request->getParameter('no_tel'), $request->getParameter('target')))
        {
            $response['content'] = "Le num&eacute;ro de t&eacute;l&eacute;phone n'est pas valide.";

            return $this->response($response);
        }

        $axialysApi = new PhcAxialysSession();
        $axialysApi->setMethod('popups_listing');

        // We need the popup_id to associate the customer with a recall request
        // As of now, we only have one popup
        $result = $axialysApi->call()->getResult();
        if (is_array($result) && !empty($result) && array_key_exists('id_popup', $result[0]))
        {
            $idPopUp = $result[0]['id_popup'];
        }
        else
        {
            $response['content'] = "Le rappel imm&eacute;diat est inactif, veuillez r&eacute;essayer ult&eacute;rieurement.";

            return $this->response($response);
        }

        // All green, initiate callback procedure
        $axialysApi->setMethod('appel');
        $axialysApi->addParameter('id_popup', $idPopUp);
        $axialysApi->addParameter('e164', $this->formattedPhoneNumber);

        // We don't want to obtain a formatted result
        $result = $axialysApi->call()->getRawResult();

        if (substr($result, 0, 2) !== 'OK')
        {
            $response = "Le rappel immédiat n'a pas pu aboutir, veuillez réessayer ultérieurement.";

            return $this->response($response);
        }

        $response['content'] = '<p>Votre demande de rappel a &eacute;t&eacute; prise en compte. Veuillez patienter, un conseiller va vous rappeler dans quelques secondes.</p>';

        if ($request->hasParameter('lettreInfo'))
        {
            $emailAddress = $request->getParameter('email');
            $prospect     = new PhcDbSfGuardUserProfile;
            $subscribed   = $prospect->updateInscriptionNewsletter(true, $emailAddress);

            $response['subscribed'] = $subscribed['result'] == 'success' ? true : false;
            $response['content'] .= '<p>Votre inscription &agrave; la lettre d\'information Son-Vid&eacute;o.com a bien &eacute;t&eacute; enregistr&eacute;e.</p>';
        }

        return $this->response($response, true);
    }

    /**
     * @param array $content
     * @param bool  $success
     * @return sfView
     */
    protected function response($content = array(), $success = false)
    {
        $this->getResponse()->setContentType('application/json');

        if (is_array($content))
        {
            foreach ($content as $key => $value)
            {
                if (is_string($value))
                {
                    $content[$key] = utf8_encode($value);
                }
            }
        }

        return $this->renderText(json_encode(array_merge(array(
            'success' => $success
        ), !is_array($content) ? array('content' => utf8_encode($content)) : $content)));
    }

    /**
     * Verify if the formatted number phone validate against e164 international standard
     *
     * @param $number
     * @param $country
     * @return bool
     */
    protected function isValidPhoneNumber($number, $country)
    {
        $className = 'PhoneNumber_' . ucfirst(strtolower($country));

        if (!class_exists($className))
        {
            return false;
        }

        $phoneNumber = new $className($number);

        if ($phoneNumber->check() && $phoneNumber->isE164Compliant())
        {
            $this->formattedPhoneNumber = $phoneNumber->formatted();

            return true;
        }

        return false;
    }
}

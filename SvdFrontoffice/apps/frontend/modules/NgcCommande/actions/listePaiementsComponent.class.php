<?php

class listePaiementsComponent extends sfComponent
{
    /**
     * @param sfRequest $request
     * @return mixed|void
     */
    function execute($request)
    {
        $request = sfContext:: getInstance()->getRequest();

        $authorized_ips = sfConfig:: get('app_ip_svd');
        $ip             = $request->getHttpHeader('addr', 'remote');
        $test_svd       = in_array($ip, $authorized_ips) ? 1 : 0;

        $obj      = $this->{$this->type};
        $colClass = 'PhcDbColFoNgcPmtPaiement' . ucfirst($this->type);
        $col      = new $colClass();
        $col->load(array(
            $obj->getId(),
            $test_svd
        ));

        $moyens = $col->getObjects(0);

        $groupes_intitules = array(
            'EnLigne'       => 'Paiement par carte bancaire',
            'Autres'        => (in_array($obj->getBoTptTransporteurId(), $obj->getDepotsEmport())) ? 'Autres moyens de paiement' : 'Virement et autres moyens de paiement',
            'Svdcc'         => 'Carte / bon cadeau Son-Vid&eacute;o.com',
            'CrtChqCadeaux' => 'Ch&egrave;ques cadeaux / cartes pr&eacute;pay&eacute;es / cartes cadeaux',
            '1ec'           => 'Cr&eacute;dit 1euro.com',
            'presto'        => 'Cr&eacute;dit Cetelem en 5, 10 ou 20 fois',
            'Nx'            => 'Paiement en 3 ou 4 fois',
        );

        $groupes_ordre = array(
            'EnLigne'       => 1,
            'Svdcc'         => 2,
            'Nx'            => 3,
            'presto'        => 4,
            'Autres'        => 5,
            '1ec'           => 6,
            'CrtChqCadeaux' => 7,
        );

        $this->grp_moyens             = array();
        $moyens_cmplt                 = array();
        $this->id_paiement_principal  = null;
        $this->id_paiement_secondaire = null;
        $pmt                          = null;
        if (array_key_exists('paiements', $this->{$this->type}->getAttributes()))
        {
            foreach ($this->{$this->type}->getPaiements() as $paiement)
            {
                if ($paiement->getComplement() == 0)
                {
                    $this->id_paiement_principal = $paiement->getPaiementId();
                    $pmt                         = $paiement;
                }
                else
                {
                    $this->id_paiement_secondaire = $paiement->getPaiementId();
                }
            }
        }

        if ($this->id_paiement_principal == 22)
        {
            $moyen = new PhcDbFoCtgPmtPaiement(22);
            $moyen->load();
            $this->grp_moyens[$groupes_ordre['Svdcc']]['groupe']   = 'Svdcc';
            $this->grp_moyens[$groupes_ordre['Svdcc']]['intitule'] = $groupes_intitules['Svdcc'];
            $this->grp_moyens[$groupes_ordre['Svdcc']]['moyens'][] = $moyen;
        }

        foreach ($moyens as $moyen)
        {
            if ($this->id_paiement_principal == 22)
            {
                $moyens_cmplt[] = $moyen;
            }
            else
            {
                $this->grp_moyens[$groupes_ordre[$moyen->getGroupe()]]['groupe']   = $moyen->getGroupe();
                $this->grp_moyens[$groupes_ordre[$moyen->getGroupe()]]['intitule'] = $groupes_intitules[$moyen->getGroupe()];
                $this->grp_moyens[$groupes_ordre[$moyen->getGroupe()]]['moyens'][] = $moyen;
            }
        }
        ksort($this->grp_moyens);
        $this->complement = 0;
        if ($this->id_paiement_principal == 22)
        {
            if (!is_null($pmt) and $pmt->getMontant() > 0)
            {
                if ($pmt->getMontant() < $this->{$this->type}->getPrixTotalTtc())
                {
                    $this->grp_moyens[$groupes_ordre['Svdcc']]['moyens'] = array_merge($this->grp_moyens[$groupes_ordre['Svdcc']]['moyens'], $moyens_cmplt);
                    $this->complement                                    = 1;
                }
                else
                {
                    if (array_key_exists('paiements', $this->{$this->type}->getAttributes()))
                    {
                        foreach ($this->{$this->type}->getPaiements() as $paiement)
                        {
                            if ($paiement->getComplement() == 1)
                            {
                                $this->{$this->type}->paiementActive($paiement->getId(), 0);
                            }
                        }
                    }
                }
            }
        }

        if ($this->id_paiement_principal == 7)
        {
            $this->complement = 1;
        }
    }
}

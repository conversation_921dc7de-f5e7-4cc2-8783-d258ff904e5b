<!-- START Google Marchands de confiance Order -->
<div id="gts-order" style="display:none;" translate="no">

  <!-- start order and merchant information -->
  <span id="gts-o-id"><?php echo $commande->getId(); ?></span>
  <span id="gts-o-domain">www.son-video.com</span>
  <span id="gts-o-email"><?php echo $commande->getEmail(); ?></span>
  <span id="gts-o-country"><?php echo $commande->getLvrPays(); ?></span>
  <span id="gts-o-currency">EUR</span>
  <span id="gts-o-total"><?php echo $commande->getPrixTotalTtc(); ?></span>
  <span id="gts-o-discounts">0</span>
  <span id="gts-o-shipping-total"><?php echo $commande->getFraisPort(); ?></span>
  <span id="gts-o-tax-total"><?php echo $commande->getPrixTotalTtc() * __TVA__; ?></span>
  <span id="gts-o-est-ship-date"><?php echo $dateExpe; ?></span>
  <span id="gts-o-est-delivery-date"><?php echo $dateLiv; ?></span>
  <?php if ($isStock == true): ?>
  <span id="gts-o-has-preorder">N</span>
  <?php else: ?>
  <span id="gts-o-has-preorder">Y</span>
  <?php endif; ?>
  <span id="gts-o-has-digital">N</span>
  <!-- end order and merchant information -->

  <!-- start repeated item specific information -->
  <?php foreach($commande->getArticles() as $article): ?>
      <span class="gts-item">
          <span class="gts-i-name"><?php echo $article->getDescriptionCourte(); ?></span>
          <span class="gts-i-price"><?php echo $article->getPrixVenteTotalTtc(); ?></span>
          <span class="gts-i-quantity"><?php echo $article->getQuantite(); ?></span>
      </span>
  <?php endforeach;?>
  <!-- end repeated item specific information -->

</div>
<!-- END Google Marchands de confiance Order -->

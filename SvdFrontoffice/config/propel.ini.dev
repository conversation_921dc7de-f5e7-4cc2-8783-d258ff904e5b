propel.targetPackage       = lib.model
propel.packageObjectModel  = true
propel.project             = phc-frontoffice
propel.database            = mysql
propel.database.driver     = mysql
propel.database.url        = mysql:dbname=PHC_frontoffice;host=localhost
propel.database.creole.url = ${propel.database.url}
propel.database.user       = backoffice
propel.database.password   = T3bn0dxJ
propel.database.encoding   = latin1

; mysql options
propel.mysql.tableType     = InnoDB

propel.addVendorInfo       = true
propel.addGenericAccessors = true
propel.addGenericMutators  = true
propel.addTimeStamp        = true
propel.addValidators       = false

propel.useDateTimeClass       = true
propel.defaultTimeStampFormat = Y-m-d H:i:s
propel.defaultTimeFormat      = H:i:s
propel.defaultDateFormat      = Y-m-d

propel.schema.validate        = false
propel.samePhpName            = false
propel.disableIdentifierQuoting     = false
propel.emulateForeignKeyConstraints = true

; directories
propel.home                    = .
propel.output.dir              = /var/www/PhC-Apps/fo__frontoffice_svd
propel.schema.dir              = ${propel.output.dir}/config
propel.conf.dir                = ${propel.output.dir}/config
propel.phpconf.dir             = ${propel.output.dir}/config
propel.sql.dir                 = ${propel.output.dir}/data/sql
propel.runtime.conf.file       = runtime-conf.xml
propel.php.dir                 = ${propel.output.dir}
propel.default.schema.basename = schema
propel.datadump.mapper.from    = *schema.xml
propel.datadump.mapper.to      = *data.xml

; builder settings
propel.builder.peer.class              = plugins.sfPropelPlugin.lib.builder.SfPeerBuilder
propel.builder.object.class            = plugins.sfPropelPlugin.lib.builder.SfObjectBuilder
propel.builder.objectstub.class        = plugins.sfPropelPlugin.lib.builder.SfExtensionObjectBuilder
propel.builder.peerstub.class          = plugins.sfPropelPlugin.lib.builder.SfExtensionPeerBuilder
propel.builder.objectmultiextend.class = plugins.sfPropelPlugin.lib.builder.SfMultiExtendObjectBuilder
propel.builder.mapbuilder.class        = plugins.sfPropelPlugin.lib.builder.SfMapBuilderBuilder

propel.builder.addIncludes  = false
propel.builder.addComments  = true
propel.builder.addBehaviors = true

<?php
class RechercheTachesCmdForm extends BaseForm
{
  public function configure()
  {
  		$this->setWidgets(array(
  		
      'Commande' => new sfWidgetFormInputText(),
  		));
  		
  	
  		$this->widgetSchema->setNameFormat('idCommande[%s]');
  		//$this->widgetSchema->setLabel('ok', 'recherche');
  	  $this->setValidators(array('Commande' => new sfValidatorAnd(array(
  	  
  	   new sfValidatorString(array('required' => false ,'max_length' => 20),
                           array('required' => 'Le num&eacute;ro de commande est requis',
                                 'max_length' => ' <span style="color:red;">"%value%" est trop long. Le num&eacute;ro de commande doit comporter entre 1 et %max_length% charact&egrave;res.</span>',
                                                                                                                                                                          )),
      					 new sfValidatorRegex(array('pattern' => '/^[0-9]{1,}$/'), array('invalid' => '<span style="color:red;">Le num&eacute;ro de commande est incorrect.</span>' )),
      					 
      					 )),
      		));
  	
  	
  }
}




?>
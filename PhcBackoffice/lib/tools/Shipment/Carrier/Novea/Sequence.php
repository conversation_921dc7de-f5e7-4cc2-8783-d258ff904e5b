<?php

/**
 * Class ShipmentCarrierNoveaSequence
 *
 * Helper class to manage Novea Sequence
 */
class ShipmentCarrierNoveaSequence
{
    protected $current_sequence;

    /**
     * @var PhcContractClientInterface
     */
    protected $client;

    /**
     * @var ShipmentCarrierNoveaSequenceEntity
     */
    protected $entity_manager;

    /**
     * @param PhcContractClientInterface $client
     */
    public function __construct(PhcContractClientInterface $client)
    {
        $this->client = $client;

        $this->entity_manager = new ShipmentCarrierNoveaSequenceEntity;
    }

    /**
     * getSequence
     *
     * @return mixed
     * @throws ErrorException
     * @throws sfException
     */
    public function getSequence()
    {
        $this->checkSequence();

        return $this->current_sequence;
    }

    /**
     * getSequenceWithAccount
     *
     * @param null $additional_parameters
     *
     * @return array
     * @throws ErrorException
     * @throws sfException
     */
    public function getSequenceWithAccount($additional_parameters = null)
    {
        $this->checkSequence();

        $account             = $this->client->getAccount('CustomerID');
        $account->IdSequence = $this->current_sequence->IdSequence;

        if (!is_null($additional_parameters))
        {
            return array_merge((array) $account, (array) $additional_parameters);
        }

        return $account;
    }

    /**
     * checkSequence
     *
     * @throws ErrorException
     * @throws sfException
     */
    protected function checkSequence()
    {
        $reload = false;

        if (is_null($this->current_sequence))
        {
            $this->loadActiveSequence();
        }

        if (!$this->activeSequenceDateIsToday() && $this->activeSequenceHasShipment())
        {
            $date_sequence = date('d/m/Y', strtotime($this->current_sequence->CreateDate));

            throw new ErrorException("[ShipmentCarrierNoveaSequence] La séquence n°{$this->current_sequence->IdSequence} du {$date_sequence} comprenant {$this->current_sequence->ShipmentCount} BL n'a pas été cloturée");
        }

        if (!$this->activeSequenceDateIsToday() && !$this->activeSequenceHasShipment())
        {
            $reload = true;

            $this->deleteActiveSequence();
        }

        if ($reload)
        {
            $this->loadActiveSequence();
        }
    }

    /**
     * loadActiveSequence
     *
     * @throws ErrorException
     * @throws sfException
     */
    protected function loadActiveSequence()
    {
        $result = $this->client->call('SequencesActives', (array) $this->client->getAccount());

        $sequences_actives_result = (array) $result->SequencesActivesResult;

        $this->current_sequence = !empty($sequences_actives_result) ? $this->extractActiveSequence($sequences_actives_result) : false;


        if (!$this->current_sequence)
        {
            $this->createSequence();
        }
    }

    /**
     * extractActiveSequence
     *
     * @param array $result
     *
     * @return bool|mixed
     * @throws sfException
     */
    protected function extractActiveSequence(array $result)
    {
        if (is_array($result['Sequence']))
        {
            $this->entity_manager->check($result['Sequence']);

            // We should never have more than one sequence active
            // And the active one might not be the last one in the array
            $active_sequences = 0;
            foreach ($result['Sequence'] as $item) {
                if (empty($item->CloseDate)) {
                    $active_sequences++;
                    $sequence = $item;
                }
            }

            // Don't block the UI though, just send back the last one and log the error in Sentry
            if ($active_sequences > 1) {
                PhcNotifier::notifyMessage('There are more than one active Novea sequence', compact('result'), 'warning');
            }

        }

        if (!is_array($result['Sequence']))
        {
            $sequence = $result['Sequence'];
            $this->entity_manager->check(array($sequence));
        }

        if (!empty($sequence) && empty($sequence->CloseDate))
        {
            return $sequence;
        }

        return false;
    }

    /**
     * createSequence
     *
     * Create a new sequence for the current expedition
     *
     * @throws ErrorException
     * @throws ErrorException
     * @throws sfException
     */
    protected function createSequence()
    {
        $result = $this->client->call('CreateSequence', (array) $this->client->getAccount());

        if ($result->CreateSequenceResult == 1)
        {
            $this->loadActiveSequence();
        }
        else
        {
            throw new ErrorException("Une erreur est survenue lors de la tentative de création d'une nouvelle séquence active pour Novéa");
        }
    }

    /**
     * deleteActiveSequence
     *
     * @throws ErrorException
     */
    protected function deleteActiveSequence()
    {
        $sequence_to_delete            = $this->current_sequence;
        $sequence_to_delete->CloseDate = date("Y-m-d\TH:i:s", strtotime("now"));

        $result = $this->client->call('DeleteSequence', array('sequence' => $sequence_to_delete));

        if ($result->DeleteSequenceResult !== 1)
        {
            throw new ErrorException("Une erreur est survenue lors de la tentative de suppression de la séquence {$sequence_to_delete->IdSequence} pour Novéa");
        }

        $this->entity_manager->persist($sequence_to_delete);
    }

    /**
     * closeActiveSequence
     *
     * @return bool
     * @throws ErrorException
     * @throws sfException
     */
    public function closeActiveSequence()
    {
        if (is_null($this->current_sequence))
        {
            $this->loadActiveSequence();
        }

        $sequence_to_close            = $this->current_sequence;
        $sequence_to_close->CloseDate = date("Y-m-d\TH:i:s", strtotime("now"));

        $result = $this->client->call('CloseSequence', array('sequence' => $sequence_to_close));

        if (!$result->CloseSequenceResult)
        {
            throw new ErrorException("Une erreur est survenue lors de la tentative de cloture de la séquence {$sequence_to_close->IdSequence} pour Novéa");
        }

        $this->entity_manager->persist($sequence_to_close);

        return true;
    }

    /**
     * getEndOfDayReportForSequence
     *
     * @param $sequence_id
     * @return string
     * @throws ErrorException
     */
    public function getEndOfDayReportForSequence($sequence_id)
    {
        $report = sfConfig::get('app_tpt_path') . 'novea/sequence/' . $sequence_id . '.pdf';

        if (!file_exists($report))
        {
            $result = $this->client->call('CourierEndOfSequence', array('idSequence' => $sequence_id));

            if (!fwrite(fopen(sfConfig::get('app_tpt_path') . 'novea/sequence/' . $sequence_id . '.pdf', 'w'), $result->CourierEndOfSequenceResult))
            {
                throw new ErrorException("Erreur lors de la recuperation du rapport pour la séquence " . $sequence_id);
            }
        }

        return $report;
    }

    /**
     * activeSequenceIsClosed
     *
     * @return bool
     */
    protected function activeSequenceIsClosed()
    {
        return !empty($this->current_sequence->CloseDate);
    }

    /**
     * activeSequenceIsToday
     *
     * @return bool
     */
    protected function activeSequenceDateIsToday()
    {
        return date('Y-m-d', strtotime($this->current_sequence->CreateDate)) == date('Y-m-d', strtotime('now'));
    }

    /**
     * activeSequenceHasShipment
     *
     * @return bool
     */
    protected function activeSequenceHasShipment()
    {
        return $this->current_sequence->ShipmentCount > 0;
    }
}

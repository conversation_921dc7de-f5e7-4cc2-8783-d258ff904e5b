<?php

class reimprimeEtiquetteAction extends phcActions
{
    public function execute($request)
    {
        if (!$request->isXmlHttpRequest()) {
            return false;
        }

        $delivery_ticket_id = (int) $request->getParameter('delivery_ticket_id');
        $printer_id         = (int) $request->getParameter('printer_id');

        try {
            if ($request->hasParameter('tracking_number') && !is_null($request->getParameter('tracking_number'))) {
                $delivery_ticket_id = $this->getDeliveryTicketFromTrackingNumber($request->getParameter('tracking_number'));
            }

            $sticker = new ShipmentSticker($delivery_ticket_id);
            $printer = new PhcPrinter();

            $printer->setPrinter($printer_id);
            $printer->send($sticker->getPathIfExists(ShipmentSticker::ARCHIVED));
        }
        catch (Exception $e) {
            return $this->response($e->getMessage());
        }

        return $this->response("Etiquette imprimée sur imprimante " . $printer_id, true);
    }

    /**
     * getDeliveryTicketFromTrackingNumber
     *
     * @param $tracking_number
     * @return mixed
     */
    private function getDeliveryTicketFromTrackingNumber($tracking_number)
    {
        $sql = <<<SQL
SELECT id_bon_livraison delivery_ticket_id
FROM colis
WHERE no_colis = :tracking_number
SQL;

        $connection = new PhcDbConnection();
        $result     = $connection->fetchObject($sql, compact('tracking_number'));

        return $result->delivery_ticket_id;
    }
}

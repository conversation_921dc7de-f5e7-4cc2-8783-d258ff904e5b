<?php $rows = sfOutputEscaperGetterDecorator::unescape($rows) ?>

<br class="clearfix"/>

<div class="table-pagination clearfix">
    <div class="pull-left">
        <?php include_partial('common/table/pagination_infos', $pagination_infos) ?>
    </div>
    <div class="pull-right">
        <?php include_partial('common/table/pagination', $pagination_infos) ?>
    </div>
</div>

<table class="BO_ui_table search-result">
    <thead>
        <tr>
            <th class="constrain">Type</th>
            <th class="constrain">Statut</th>
            <th class="constrain">ID</th>
            <th>SKU</th>
        </tr>
    </thead>
    <tbody>
        <?php if(!empty($rows)): ?>
            <?php foreach($rows as $row): ?>
            <tr>
                <td class="constrain"><?php echo $row['produitType'] ?></td>
                <td class="constrain"><?php echo $row['statut'] ?></td>
                <td class="constrain">
                    <a href="" class="btn btn-mini btn-select" data-id="<?php echo $row['id'] ?>">
                        <i class="fa fa-hand-o-right"></i><?php echo $row['id'] ?>
                    </a>
                </td>
                <td>
                    <strong><?php echo $row['reference'] ?></strong> -  <?php echo $row['marque'] ?> <?php echo $row['modele'] ?>
                    <a href="<?php echo ERP_LEGACY_ROOT_URL ?>/produit/ficheArticle?id_produit=<?php echo $row['id'] ?>"
                       target="_blank"
                       class="btn btn-mini btn-icon">
                        <i class="fa fa-eye"></i>
                    </a>
                </td>
            </tr>
            <?php endforeach ?>
        <?php else: ?>
            <tr>
                <td colspan="4" class="error">
                    Aucun résultat trouvé
                </td>
            </tr>
        <?php endif ?>
    </tbody>
</table>

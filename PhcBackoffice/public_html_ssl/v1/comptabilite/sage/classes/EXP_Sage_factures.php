<?php

// require_once FW_ROOT . "php/classes/debug.class.php";
class EXP_Sage_factures extends EXP_Sage
{

    public function recup_documents()
    {
        $cnx = new Bdd_connexion(DB_HOST, BO_DB_USER_GENERAL, BO_DB_PASSWD_GENERAL, BO_DB_BASE);

        $requete = array();

        // Si c'est cilo, on utilise leur adresse car c'est eux que l'on facture.
        // Idéalement, il faudrait que cela soit lié à la table BO_EXP_expeditions_expediteurs ou quelque chose de semblable
        $requete[] = <<<SQL
SELECT
  RIGHT(
    CONCAT('0000000', c.id_prospect),
    7
  ) AS no_client,
  CASE
    WHEN (f.cnt_fct_societe = '')
    THEN (
      UPPER(
        CONCAT(
          f.cnt_fct_nom,
          ' ',
          f.cnt_fct_prenom
        )
      )
    )
    ELSE (UPPER(f.cnt_fct_societe))
  END AS 'client',
  DATE_FORMAT(f.date_creation, '%Y%m%d') AS date_facture,
  DATE_FORMAT(IFNULL(f1.date_creation, f.date_creation), '%Y%m%d') AS date_tva,
  f.date_creation,
  DATE_FORMAT(f.date_creation, '%Y/%m') AS annee_mois,
  f.id_facture,
  f.type,
  f.id_commande,
  f.detaxe_export,
  UPPER(f.cnt_fct_nom) AS nom,
  f.cnt_fct_prenom AS prenom,
  pays.code_3_lettres,
  pays.id_pays,
  pays.cee,
  f.cnt_fct_numero_tva AS intracomm,
  pays.dom_tom,
  sys_taux.taux AS taux,
  CASE
    WHEN (
      f.cnt_fct_email LIKE '%@marketplace.amazon.co.uk'
    )
    THEN 1
    ELSE 0
  END AS cmdo,
  if(b2b.id_commande is null, 'particulier', 'entreprise') AS type_client
FROM
  facture f
  LEFT JOIN BO_SYS_taux sys_taux
    ON DATE_FORMAT(f.date_creation, '%Y%m%d') = DATE_FORMAT(sys_taux.date, '%Y%m%d')
  LEFT JOIN produit_facture pf
    ON f.id_facture = pf.id_facture
  LEFT JOIN facture f1
    ON pf.id_facture_origine = f1.id_facture
  LEFT JOIN backOffice.commande_btob AS b2b
    ON f.id_commande = b2b.id_commande,
  pays,
  commande c
WHERE f.id_commande = c.id_commande
  AND IF(c.creation_origine in ('cilo.dk'), f.cnt_fct_id_pays = pays.id_pays, f.cnt_lvr_id_pays = pays.id_pays)
  AND (c.creation_origine != 'ecranlounge.com' OR c.date_creation < '2022-12-01')
SQL;

        $requete[] = "  AND DATE(f.date_creation) BETWEEN '{$this->interval['date_debut']}' AND '{$this->interval['date_fin']}'";

        // For debugging purpose
        //$requete[] = "  AND c.id_commande IN (1053716)";

        $requete[] = $this->amzuk == 'AMZUK' ? "  AND c.cnt_fct_email LIKE '%@marketplace.amazon.co.uk'" : "  AND c.cnt_fct_email NOT LIKE '%@marketplace.amazon.co.uk'";

        $requete[] = "GROUP BY f.id_facture ORDER BY f.id_facture ASC, f.date_creation ASC ;";

        $requete = implode("\n", $requete);

        $rqt = new Bdd_requete($requete);

        foreach ($rqt->resultat as $rangee)
        {

            $facture = array();

            $facture['id_facture']    = $rangee['id_facture'];
            $facture['type']          = $rangee['type'];
            $facture['id_commande']   = $rangee['id_commande'];
            $facture['no_client']     = $rangee['no_client'];
            $facture['type_client']   = $rangee['type_client'];
            $facture['client']        = strtoupper($rangee['client']);
            $facture['date_facture']  = $rangee['date_facture'];
            $facture['date_tva']      = $rangee['date_tva'];
            $facture['date_creation'] = $rangee['date_creation'];
            $facture['annee_mois']    = $rangee['annee_mois'];
            $facture['id_facture']    = $rangee['id_facture'];
            $facture['detaxe_export'] = $rangee['detaxe_export'];
            $facture['nom']           = $this->formate_export_factures($rangee['nom']);
            $facture['prenom']        = $this->formate_export_factures($rangee['prenom']);
            $facture['id_pays']       = $rangee['id_pays'];
            $facture['pays']          = $rangee['code_3_lettres'];
            $facture['cee']           = $rangee['cee'];
            $facture['intracomm']     = $rangee['intracomm'];
            $facture['dom_tom']       = $rangee['dom_tom'];
            $facture['cmdo']          = $rangee['cmdo'];
            $facture['taux']          = $rangee['taux']; // taux ?  currency conversion rate UK - Euro

            // PAIMENTS
            $requete = "SELECT pa.id_paiement ";
            $requete .= "FROM paiement pa, ";
            $requete .= "   paiement_facture pa_f ";
            $requete .= "WHERE pa.id_paiement = pa_f.id_paiement ";
            $requete .= "   AND pa_f.montant > 0 ";
            $requete .= "   AND pa_f.id_facture = '" . $facture["id_facture"] . "' ";
            $requete .= "ORDER BY pa_f.id_unique ASC ;";
            $rqt_tmp                = new Bdd_requete($requete);
            $id_paiement            = ($rqt_tmp->nb_selection > 1) ? '999' : $rqt_tmp->resultat[0]['id_paiement'];
            $facture["id_paiement"] = $id_paiement;

            // FRAIS DE PORT (+INTERNATIONAL)
            $requete = "SELECT SUM(ABS(quantite * prix_vente)) AS montant_ttc , AVG(tva) AS tva            ";
            $requete .= "FROM produit_facture ";
            $requete .= "WHERE id_produit IN (8123, 8138) ";
            $requete .= "   AND id_facture = '" . $facture["id_facture"] . "' ";
            $requete .= "GROUP BY id_facture ;";
            $rqt_tmp        = new Bdd_requete($requete);
            $frais_port_TTC = $rqt_tmp->resultat[0]['montant_ttc'];
            $tva_port       = $rqt_tmp->resultat[0]['tva'];

            // MONTANT GARANTIE
            $requete = "SELECT SUM(ABS(FCT_PDT_px_garantie_ext_net_ht(id_facture, id_produit, id_unique))) AS montant_grt_ext_ht, ";
            $requete .= "   SUM(ABS(FCT_PDT_px_garantie_ext_net(id_facture, id_produit, id_unique))) AS montant_grt_ext_ttc, ";
            $requete .= "   SUM(ABS(FCT_PDT_px_garantie_vc_net(id_facture, id_produit, id_unique))) AS montant_grt_vc_ttc ";
            $requete .= "FROM produit_facture ";
            $requete .= "WHERE id_facture = '" . $facture["id_facture"] . "' ";
            $requete .= "GROUP BY id_facture ;";
            $rqt_tmp      = new Bdd_requete($requete);
            $garantie_ext = (datetime_to_unix_timestamp($facture['date_creation']) > datetime_to_unix_timestamp('2009-04-16 14:57:00')) ? $rqt_tmp->resultat[0]['montant_grt_ext_ht'] : $rqt_tmp->resultat[0]['montant_grt_ext_ttc'];
            $garantie_vc  = $rqt_tmp->resultat[0]['montant_grt_vc_ttc'];

            // CR
            $requete = "SELECT SUM(ABS(quantite * prix_vente)) AS montant_ttc , AVG(tva) AS tva        ";
            $requete .= "FROM produit_facture ";
            $requete .= "WHERE id_produit = 8124 ";
            $requete .= "   AND id_facture = '" . $facture["id_facture"] . "' ";
            $requete .= "GROUP BY id_facture ;";
            $rqt_tmp        = new Bdd_requete($requete);
            $cr_TTC         = $rqt_tmp->resultat[0]['montant_ttc'];
            $tva_surcout_cr = $rqt_tmp->resultat[0]['tva'];

            // CARTES CADEAUX
            $requete = "SELECT SUM(ABS(pf.quantite * pf.prix_vente)) AS montant_ttc , AVG(pf.tva) AS tva         ";
            $requete .= "FROM produit_facture pf, ";
            $requete .= "   produit p ";
            $requete .= "WHERE pf.id_produit = p.id_produit ";
            $requete .= "   AND (p.reference LIKE 'CARTECADEAUSVD%' OR p.reference LIKE 'BONCADEAUSVD%') ";
            $requete .= "   AND pf.id_facture = '" . $facture["id_facture"] . "' ";
            $requete .= "GROUP BY id_facture ";
            $rqt_tmp           = new Bdd_requete($requete);
            $cartes_cadeau_TTC = $rqt_tmp->resultat[0]['montant_ttc'];
            $tva_cc            = $rqt_tmp->resultat[0]['tva'];

            // CARTES CADEAUX VENTE PRIVEE
            // de la reference de la carte cadeau on déduit l'opération spéciale et le montant
            $requete              = "SELECT SUM(ABS(pf.quantite * pf.prix_vente)) AS montant_ttc, AVG(pf.tva) AS tva FROM produit_facture pf, produit p WHERE pf.id_produit = p.id_produit AND (p.reference REGEXP 'BONCADSVD[0-9]{1,5}VP') AND pf.id_facture = '" . $facture["id_facture"] . "' GROUP BY id_facture ";
            $rqt_tmp              = new Bdd_requete($requete);
            $cartes_cadeau_vp_TTC = $rqt_tmp->resultat[0]['montant_ttc'];
            $tva_ccvp             = $rqt_tmp->resultat[0]['tva'];
            // correction du numéro de client pour le compte 411000  - TODO create dedicated column "no_client" in "facture" table
            // recherche des commandes facturées au moment de l'utilisation de la carte
            if ($cartes_cadeau_vp_TTC > 0)
            {
                $cartes_cadeau_vp_type = 'VP';
            }
            // l'utilisateur n'est pas Rosedeal mais le client qui a payé avec la carte
            // 1158388 -> 04/16
            // 1358086 -> 04/17
            // 1450428 -> 04/18
            // 1527061 -> 05/19
            $vp_accounts = array('1158388', '1358086', '1450428', '1527061');
            if (in_array($facture['no_client'], $vp_accounts) && $facture['type'] == 'avoir')
            {
                $requete = "SELECT RIGHT(CONCAT('0000000', c.id_prospect), 7) AS no_client, UPPER(CONCAT(c.cnt_fct_nom, ' ', c.cnt_fct_prenom)) AS client FROM PMT_carte_cadeau cc JOIN commande c ON c.id_commande = cc.utilisation_id_commande WHERE utilisation_date >= (SELECT DATE_ADD(f.date_creation, INTERVAL -1 SECOND) FROM facture f WHERE f.id_facture = " . $facture["id_facture"] . " ) AND utilisation_date <= (SELECT DATE_ADD(f.date_creation, INTERVAL +1 SECOND) FROM facture f WHERE f.id_facture = " . $facture["id_facture"] . " );";

                $rqt_tmp = new Bdd_requete($requete);
                if (isset($rqt_tmp->resultat[0]))
                {
                    $facture['no_client'] = $rqt_tmp->resultat[0]['no_client'];
                    $facture['client']    = $rqt_tmp->resultat[0]['client'];
                }
                else
                {
                    $requete = "SELECT RIGHT(CONCAT('0000000', c.id_prospect), 7) AS no_client, UPPER(CONCAT(c.cnt_fct_nom, ' ', c.cnt_fct_prenom)) AS client
FROM facture f
INNER JOIN commande c ON replace(commentaire, 'Commande ', '') = c.id_commande
where f.id_facture=" . $facture["id_facture"] . ";";

                    $rqt_tmp = new Bdd_requete($requete);
                    if (isset($rqt_tmp->resultat[0]))
                    {
                        $facture['no_client'] = $rqt_tmp->resultat[0]['no_client'];
                        $facture['client']    = $rqt_tmp->resultat[0]['client'];
                    }
                }
            }

            if ($cartes_cadeau_vp_TTC === 0 || $cartes_cadeau_vp_TTC === null) {
                // CARTES CADEAUX BLUEDEAL
                // de la reference de la carte cadeau on déduit l'opération spéciale et le montant
                $requete              = "SELECT SUM(ABS(pf.quantite * pf.prix_vente)) AS montant_ttc, AVG(pf.tva) AS tva FROM produit_facture pf, produit p WHERE pf.id_produit = p.id_produit AND (p.reference = 'BONCADSVDBLUEDEAL300') AND pf.id_facture = '" . $facture["id_facture"] . "' GROUP BY id_facture ";
                $rqt_tmp              = new Bdd_requete($requete);
                $cartes_cadeau_vp_TTC = $rqt_tmp->resultat[0]['montant_ttc'];
                $tva_ccvp             = $rqt_tmp->resultat[0]['tva'];
                // The customer order id is on credit note comment
                // recherche des commandes facturées au moment de l'utilisation de la carte
                if ($cartes_cadeau_vp_TTC > 0)
                {
                    $cartes_cadeau_vp_type = 'BLUEDEAL';
                }
                // l'utilisateur n'est pas Bluedeal/SVD mais le client qui a payé avec la carte
                // 1480752 -> Bluedeal 20-22/10/18
                $vp_accounts = array('1480752');
                if (in_array($facture['no_client'], $vp_accounts) && $facture['type'] == 'avoir')
                {
                    $requete = "SELECT RIGHT(CONCAT('0000000', c.id_prospect), 7) AS no_client, UPPER(CONCAT(c.cnt_fct_nom, ' ', c.cnt_fct_prenom)) AS client FROM PMT_carte_cadeau cc JOIN commande c ON c.id_commande = cc.utilisation_id_commande WHERE utilisation_date >= (SELECT DATE_ADD(f.date_creation, INTERVAL -1 SECOND) FROM facture f WHERE f.id_facture = " . $facture["id_facture"] . " ) AND utilisation_date <= (SELECT DATE_ADD(f.date_creation, INTERVAL +1 SECOND) FROM facture f WHERE f.id_facture = " . $facture["id_facture"] . " );";

                    $rqt_tmp = new Bdd_requete($requete);
                    if (isset($rqt_tmp->resultat[0]))
                    {
                        $facture['no_client'] = $rqt_tmp->resultat[0]['no_client'];
                        $facture['client']    = $rqt_tmp->resultat[0]['client'];
                    }
                }
            }

            // MONTANT CDISCOUNT
            $tva_cdiscount = 0;
            $requete       = "SELECT ABS(FCT_mnt_cdiscount_product_ttc (" . $facture["id_facture"] . ")) AS cdiscount_ttc,  ABS(FCT_mnt_cdiscount_product_ht (" . $facture["id_facture"] . ")) AS cdiscount_ht;";
            $rqt_tmp       = new Bdd_requete($requete);
            $cdiscount_ttc = $rqt_tmp->resultat[0]['cdiscount_ttc'];
            $cdiscount_ht  = $rqt_tmp->resultat[0]['cdiscount_ht'];
            $tva_cdiscount = $cdiscount_ttc - $cdiscount_ht;

            // MONTANT FACTURE
            // le calcul est fait à partir de la tva enregistrés dans la facture alors que pour l'écotaxe ...
            // le montant HT est recalculé en fonction du taux de tva retrouvé ici
            $requete = "SELECT ABS(FCT_mnt_ttc (" . $facture["id_facture"] . ")) AS montant_ttc, ";
            $requete .= "   ABS(FCT_mnt_ht (" . $facture["id_facture"] . ")) AS montant_ht ;";
            $rqt_tmp     = new Bdd_requete($requete);
            $montant_TTC = $rqt_tmp->resultat[0]['montant_ttc'];
            $montant_HT  = $rqt_tmp->resultat[0]['montant_ht'] + $tva_cdiscount;

            // ECOTAXE - SORECOP
            $requete     = "SELECT ABS(FCT_mnt_ecotaxe_ttc (" . $facture["id_facture"] . ")) AS ecotaxe_ttc ;";
            $rqt_tmp     = new Bdd_requete($requete);
            $ecotaxe_TTC = $rqt_tmp->resultat[0]['ecotaxe_ttc'];

            $requete     = "SELECT ABS(FCT_mnt_sorecop_ttc (" . $facture["id_facture"] . ")) AS sorecop_ttc ;";
            $rqt_tmp     = new Bdd_requete($requete);
            $sorecop_TTC = $rqt_tmp->resultat[0]['sorecop_ttc'];

            // On utilise la date de création de la facture d'origine si c'est un avoir, cela permet de retrouver la bonne tva
            // sur les vieilles commandes (antérieur au 01/01/2014)
            $requete     = "SELECT GET_taux_tva('" . $facture['id_pays'] . "','standard', '" . $facture['date_tva'] . "') AS tva_taux ;";
            $rqt_tmp     = new Bdd_requete($requete);
            $tva_ecotaxe = (float) $rqt_tmp->resultat[0]['tva_taux'];  // TODO: change the product tax management ...
            $tva_sorecop = $tva_ecotaxe;

            // MONTANT SERVICE
            $requete = "SELECT ABS(FCT_serv_dep_mnt_ht (" . $facture["id_facture"] . ")) AS montant_serv_dep_ht,";
            $requete .= "   ABS(FCT_serv_inst_mnt_ht (" . $facture["id_facture"] . ")) AS montant_serv_inst_ht ;";
            $rqt_tmp     = new Bdd_requete($requete);
            $montant_serv_dep_ht = $rqt_tmp->resultat[0]['montant_serv_dep_ht'];
            $montant_serv_inst_ht = $rqt_tmp->resultat[0]['montant_serv_inst_ht'];


            $frais_port_HT       = round(($frais_port_TTC / (1 + $tva_port)), 2);
            $cr_HT               = round(($cr_TTC / (1 + $tva_surcout_cr)), 2);
            $cartes_cadeau_HT    = round(($cartes_cadeau_TTC / (1 + $tva_cc)), 2);
            $cartes_cadeau_vp_HT = round(($cartes_cadeau_vp_TTC / (1 + $tva_ccvp)), 2);
            $ecotaxe_HT          = round(($ecotaxe_TTC / (1 + $tva_ecotaxe)), 2);
            $sorecop_HT          = round(($sorecop_TTC / (1 + $tva_sorecop)), 2);
            $vente_materiel_HT   = $montant_HT - ($frais_port_HT + $cr_HT + $ecotaxe_HT + $sorecop_HT);

            if ($tva_port == 0.196 || $tva_surcout_cr == 0.196 || $tva_cc == 0.196 | $tva_ccvp == 0.196 || $tva_ecotaxe == 0.196)  // TODO: change the product tax management ..
            {
                $facture['tva_taux'] = 0.196;
            }
            else
            {
                $facture['tva_taux'] = $tva_ecotaxe;
            }

            if ($facture['detaxe_export'] == 'oui')
            {
                $montant_TTC = $montant_HT;
            }
            $tva = $montant_TTC - $montant_HT;

            //hack bug php sur la soustraction de type 1911.37 - 0.00 - 0.00 - 1911.37 retourne un résultat en notation scientifique au lieu de 0
            // we use ttc for cdiscount because we can't handle tva variation on product level but only on order level
            $facture['vente_materiel']     = sprintf("%01.2f", $vente_materiel_HT - $garantie_ext - $garantie_vc - $cartes_cadeau_HT - $cartes_cadeau_vp_HT - $cdiscount_ttc - $montant_serv_dep_ht - $montant_serv_inst_ht);
            $facture['port']               = $frais_port_HT;
            $facture['cr']                 = $cr_HT;
            $facture['cartes_cadeau']      = $cartes_cadeau_HT;
            $facture['cartes_cadeau_vp']   = $cartes_cadeau_vp_HT;
            $facture['cartes_cadeau_type'] = $cartes_cadeau_vp_type;
            $facture['tva']                = $tva;
            $facture['ecotaxe']            = $ecotaxe_HT;
            $facture['sorecop']            = $sorecop_HT;
            $facture['garantie_ext']       = $garantie_ext;
            $facture['garantie_vc']        = $garantie_vc;
            $facture['cdiscount_ttc']      = $cdiscount_ttc;

            $facture['montant_TTC'] = $montant_TTC;
            $facture['montant_HT']  = $montant_HT;

            $facture['vente_service']         = $montant_serv_dep_ht + $montant_serv_inst_ht;
            $facture['montant_serv_dep_ht']   = $montant_serv_dep_ht;
            $facture['montant_serv_inst_ht']  = $montant_serv_inst_ht;

            unset ($cr_TTC, $frais_port_TTC, $tva, $montant_TTC, $montant_HT, $vente_materiel_HT, $cr_HT, $frais_port_HT, $ecotaxe_HT, $ecotaxe_TTC, $sorecop_HT, $sorecop_TTC, $cartes_cadeau_vp_TTC, $cartes_cadeau_HT, $cartes_cadeau_vp_HT, $cartes_cadeau_vp_type,$montant_serv_dep_ht,$montant_serv_inst_ht);

            $this->documents[] = $facture;

            unset ($facture, $rangee);
        }

        $this->forge_fichier_sage();
    }

###
##    FORMAT DU FICHIER
#
#   JOURNAL           (6)   -> CE - BQ - OD - VC - VUK
#   DATE D'ECRITURE   (8)   -> AAAAMMDD
#   COMPTE GENERAL    (13)  -> 411000 - 512000 - 707200 - 445700
#   FACTURE           (17)  ->
#   REFERENCE         (17)  -> IDEM DU PRECEDENT
#   COMPTE TIERS      (17)  -> ??
#   LIBELLE ECRITURE  (35)  -> MMONTANT A GAUCHE
#   CREDIT | DEBIT    (1)   -> D ou C
#   MONTANT           (14)  ->id_commande
#
##
###
    protected function forge_fichier_sage()
    {
        $compte_pays = ['DEU' => ['vente_materiel' => '707127', 'port' => '708223', 'ecotaxe' => '707153', 'sorecop' => '707168', 'garantie_ext' => '708504', 'tva' => '445703'],
                        'AUT' => ['vente_materiel' => '707116', 'port' => '708236', 'ecotaxe' => '707143', 'sorecop' => '707178', 'garantie_ext' => '708520', 'tva' => '445709'],
                        'BEL' => ['vente_materiel' => '707121', 'port' => '708221', 'ecotaxe' => '707159', 'sorecop' => '707169', 'garantie_ext' => '708502', 'tva' => '445702'],
                        'BGR' => ['vente_materiel' => '707115', 'port' => '708237', 'ecotaxe' => '707142', 'sorecop' => '707179', 'garantie_ext' => '708521', 'tva' => '445715'],
                        'CYP' => ['vente_materiel' => '707114', 'port' => '708238', 'ecotaxe' => '707141', 'sorecop' => '707180', 'garantie_ext' => '708522', 'tva' => '445718'],
                        'HRV' => ['vente_materiel' => '707113', 'port' => '708239', 'ecotaxe' => '707140', 'sorecop' => '707181', 'garantie_ext' => '708523', 'tva' => '445719'],
                        'DNK' => ['vente_materiel' => '707112', 'port' => '708219', 'ecotaxe' => '707139', 'sorecop' => '707182', 'garantie_ext' => '708524', 'tva' => '445721'],
                        'ESP' => ['vente_materiel' => '707128', 'port' => '708224', 'ecotaxe' => '707148', 'sorecop' => '707183', 'garantie_ext' => '708505', 'tva' => '445704'],
                        'EST' => ['vente_materiel' => '707111', 'port' => '708218', 'ecotaxe' => '707138', 'sorecop' => '707184', 'garantie_ext' => '708525', 'tva' => '445722'],
                        'FIN' => ['vente_materiel' => '707110', 'port' => '708217', 'ecotaxe' => '707137', 'sorecop' => '707185', 'garantie_ext' => '708526', 'tva' => '445723'],
                        'GRC' => ['vente_materiel' => '707109', 'port' => '708216', 'ecotaxe' => '707136', 'sorecop' => '707186', 'garantie_ext' => '708527', 'tva' => '445724'],
                        'HUN' => ['vente_materiel' => '707108', 'port' => '708215', 'ecotaxe' => '707135', 'sorecop' => '707187', 'garantie_ext' => '708528', 'tva' => '445725'],
                        'IRL' => ['vente_materiel' => '707107', 'port' => '708214', 'ecotaxe' => '707134', 'sorecop' => '707188', 'garantie_ext' => '708529', 'tva' => '445726'],
                        'ITA' => ['vente_materiel' => '707129', 'port' => '708232', 'ecotaxe' => '707147', 'sorecop' => '707189', 'garantie_ext' => '708506', 'tva' => '445705'],
                        'LVA' => ['vente_materiel' => '707106', 'port' => '708213', 'ecotaxe' => '707165', 'sorecop' => '707190', 'garantie_ext' => '708530', 'tva' => '445727'],
                        'LTU' => ['vente_materiel' => '707105', 'port' => '708212', 'ecotaxe' => '707166', 'sorecop' => '707191', 'garantie_ext' => '708531', 'tva' => '445728'],
                        'LUX' => ['vente_materiel' => '707119', 'port' => '708233', 'ecotaxe' => '707146', 'sorecop' => '707192', 'garantie_ext' => '708507', 'tva' => '445706'],
                        'MLT' => ['vente_materiel' => '707104', 'port' => '708211', 'ecotaxe' => '707167', 'sorecop' => '707193', 'garantie_ext' => '708532', 'tva' => '445729'],
                        'NLD' => ['vente_materiel' => '707118', 'port' => '708234', 'ecotaxe' => '707145', 'sorecop' => '707194', 'garantie_ext' => '708508', 'tva' => '445707'],
                        'POL' => ['vente_materiel' => '707103', 'port' => '708209', 'ecotaxe' => '707171', 'sorecop' => '707195', 'garantie_ext' => '708533', 'tva' => '445731'],
                        'PRT' => ['vente_materiel' => '707117', 'port' => '708235', 'ecotaxe' => '707144', 'sorecop' => '707196', 'garantie_ext' => '708509', 'tva' => '445708'],
                        'ROM' => ['vente_materiel' => '707102', 'port' => '708208', 'ecotaxe' => '707172', 'sorecop' => '707197', 'garantie_ext' => '708534', 'tva' => '445732'],
                        'SVK' => ['vente_materiel' => '707101', 'port' => '708207', 'ecotaxe' => '707173', 'sorecop' => '707198', 'garantie_ext' => '708535', 'tva' => '445733'],
                        'SVN' => ['vente_materiel' => '707131', 'port' => '708206', 'ecotaxe' => '707174', 'sorecop' => '707199', 'garantie_ext' => '708536', 'tva' => '445734'],
                        'SWE' => ['vente_materiel' => '707132', 'port' => '708205', 'ecotaxe' => '707176', 'sorecop' => '707201', 'garantie_ext' => '708537', 'tva' => '445735'],
                        'CZE' => ['vente_materiel' => '707133', 'port' => '708204', 'ecotaxe' => '707177', 'sorecop' => '707202', 'garantie_ext' => '708538', 'tva' => '445736']];

        $factures = $this->documents;
        foreach ($factures as $facture)
        {
            if ($facture['montant_TTC'] == 0)
            {
                continue;
            } // on n'exporte pas les factures dont le montant est nul

            $credit  = 0;
            $debit   = 0;
            $journal = ($facture['cmdo'] == 1) ? str_pad('VUK', 6) : str_pad('VE', 6);

            $date_ecriture   = $facture['date_facture'];
            $no_facture      = str_pad($facture['id_facture'], 17);
            $ref             = str_pad($facture['id_commande'], 17);
            $numerocommande  = str_pad($facture['id_commande'], 13);
            $no_compte_tiers = str_pad($facture['no_client'], 17);

            $lettre_credit = ($facture['type'] == 'facture') ? 'C' : 'D';
            $lettre_debit  = ($facture['type'] == 'facture') ? 'D' : 'C';

            $taux = ($facture['cmdo'] == 1) ? $facture['taux'] : 1;

            // garantie 5 ans
            if ($facture['garantie_ext'] <> 0)
            {
                $mnt = round($facture['garantie_ext'] * $taux, 2);

                if ($facture['detaxe_export'] == 'oui')
                {
                    $no_compte_grt = '708810';
                }
                else
                {
                    if (strtotime($facture['date_facture']) > strtotime('2021-10-01') && array_key_exists($facture['pays'], $compte_pays)) {
                        $no_compte_grt = $compte_pays[$facture['pays']]['garantie_ext'];
                    } else {
                        $no_compte_grt = '708500';
                    }
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $no_compte_grt, 'EXTENSION GARANTIE', $mnt);
                $credit += $mnt;
            }

            // garantie vol-casse
            if ($facture['garantie_vc'] <> 0)
            {
                $mnt = round($facture['garantie_vc'] * $taux, 2);
                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, '791600', 'GARANTIE VOL-CASSE', $mnt);
                $credit += $facture['garantie_vc'] * $taux;
            }

            // vente frais de port
            if ($facture['port'] <> 0)
            {
                $label  = 'PORT';
                $mnt    = round($facture['port'] * $taux, 2);

                if (strtotime($facture['date_facture']) < strtotime('2021-10-01')){
                    if ($facture['pays'] === 'BEL' && $facture['tva_taux'] == 0.210) // Belgique 21%
                    {
                        $compte = '708221';
                    }
                    elseif ($facture['pays'] === 'DEU')
                    {
                        $compte = '708223';
                    }
                    elseif ($facture['tva_taux'] == 0.196)
                    {
                        $compte = '708225';
                    } // 19.6%
                    else
                    {
                        $compte = '708200';
                    } // 20%
                } else {
                    if (array_key_exists($facture['pays'], $compte_pays))
                    {
                        $compte = $compte_pays[$facture['pays']]['port'];
                    }
                    elseif ($facture['tva_taux'] == 0.196)
                    {
                        $compte = '708225';
                    } // 19.6%
                    else
                    {
                        $compte = '708200';
                    } // 20%
                }

                if ($facture['detaxe_export'] == 'oui')
                {
                    // export
                    if ($facture['cee'] == 'non' or $facture['dom-tom'] == 'DOM' or $facture['dom-tom'] == 'TOM' || $facture['cee'] == 'oui' && empty($facture['intracomm']))
                    {
                        $compte = 708220;
                        $label  = 'PORT EXPORT';
                    }
                    // intra communautaire CEE
                    elseif ($facture['cee'] == 'oui')
                    {
                        $compte = '708222';
                    }
                }

                // intra groupe
                switch ($this->isIntraGroup($facture['no_client']))
                {
                    case 1:
                        $compte = '708510';
                        break;
                    case 2:
                        $compte = '708226';
                        break;
                    case 3:
                        $compte = '708512';
                        break;
                    case 4:
                        $compte = '708227';
                        break;
                    case 5:
                        $compte = '708230';
                        break;
                    case 6:
                        $compte = '708231';
                        break;
                }

                // Amazon UK
                if ($facture['cmdo'] == 1)
                {
                    $compte = '708210';
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $compte, $label, $mnt);
                $credit += $mnt;
            }

            // cr
            if ($facture['cr'] <> 0)
            {
                $mnt = round($facture['cr'] * $taux, 2);
                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, '707700', 'CONTRE REMBOURSEMENT', $mnt);
                $credit += $mnt;
            }

            // carte cadeau bon cadeau
            if ($facture['cartes_cadeau'] <> 0)
            {
                $mnt = round($facture['cartes_cadeau'] * $taux, 2);

                if ($facture['detaxe_export'] == 'oui')
                {
                    $no_compte_ccbc = '487110';
                }
                else
                {
                    if (strtotime($facture['date_facture']) < strtotime('2021-10-01')){
                        if ($facture['pays'] === 'BEL' && $facture['tva_taux'] == 0.210) // Belgique 21%
                        {
                            $no_compte_ccbc = '487300';
                        }
                        elseif ($facture['pays'] === 'DEU')
                        {
                            $no_compte_ccbc = '487330';
                        }
                        elseif ($facture['tva_taux'] == 0.196)
                        {
                            $no_compte_ccbc = '487100';
                        } // 19.6%
                        else
                        {
                            $no_compte_ccbc = '487200';
                        } // 20%
                    } else {
                        if (array_key_exists($facture['pays'], $compte_pays))
                        {
                            $no_compte_ccbc = 487201;
                        }
                        elseif ($facture['tva_taux'] == 0.196)
                        {
                            $no_compte_ccbc = '487100';
                        } // 19.6%
                        else
                        {
                            $no_compte_ccbc = '487200';
                        } // 20%
                    }
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $no_compte_ccbc, 'CARTES CADEAU', $mnt);
                $credit += $mnt;
            }

            //  bon cadeau vente privée
            if ($facture['cartes_cadeau_vp'] <> 0)
            {
                $mnt = round($facture['cartes_cadeau_vp'] * $taux, 2);

                $imputation487 = array(
                    'VP'      => '487120',
                    'GROUPON' => '487130',
                    'BLUEDEAL' => '487125'
                );

                if ($facture['detaxe_export'] == 'oui')
                {
                    $no_compte_ccbc = '487110';
                }
                else
                {
                    $no_compte_ccbc = $imputation487[$facture['cartes_cadeau_type']];
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $no_compte_ccbc, 'CARTES CADEAU ' . $facture['cartes_cadeau_type'], $mnt);
                $credit += $mnt;
            }

            // cdiscount bank product
            if ($facture['cdiscount_ttc'] <> 0)
            {
                $mnt = $facture['cdiscount_ttc'];

                $compte = 791700;

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $compte, $facture['client'], $mnt);
                $credit += $mnt;
            }

            // tva
            if ($facture['tva'] <> 0)
            {
                $mnt = round($facture['tva'] * $taux, 2);

                if (strtotime($facture['date_facture']) < strtotime('2021-10-01')){
                    if ($facture['pays'] === 'BEL' && $facture['tva_taux'] == 0.210) // Belgique 21%
                    {
                        $compte = '445702';
                    }
                    elseif ($facture['pays'] === 'DEU')
                    {
                        $compte = '445703';
                    }
                    elseif ($facture['tva_taux'] == 0.196)
                    {
                        $compte = '445700';
                    } // 19.6%
                    else
                    {
                        $compte = ($facture['cmdo'] == 1) ? '445701' : '445716';
                    } // 20%
                } else {
                    if (array_key_exists($facture['pays'], $compte_pays))
                    {
                        $compte = $compte_pays[$facture['pays']]['tva'];
                    } elseif ($facture['tva_taux'] == 0.196)
                    {
                        $compte = '445700';
                    } // 19.6%
                    else
                    {
                        $compte = ($facture['cmdo'] == 1) ? '445701' : '445716';
                    } // 20%
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $compte, 'TVA COLLECTEE', $mnt);
                $credit += $mnt;
            }

            // vente ecotaxe
            if ($facture['ecotaxe'] <> 0)
            {
                $label  = 'ECOTAXE';
                $mnt    = round($facture['ecotaxe'] * $taux, 2);

                if (strtotime($facture['date_facture']) < strtotime('2021-10-01')){
                    if ($facture['pays'] === 'BEL' && $facture['tva_taux'] == 0.210) // Belgique 21%
                    {
                        $compte = '707159';
                    }
                    elseif ($facture['pays'] === 'DEU')
                    {
                        $compte = '707153';
                    }
                    elseif ($facture['tva_taux'] == 0.196)
                    {
                        $compte = '707175';
                    } // 19.6%
                    else
                    {
                        $compte = '707150';
                    } // 20%
                } else {
                    if (array_key_exists($facture['pays'], $compte_pays))
                    {
                        $compte = $compte_pays[$facture['pays']]['ecotaxe'];
                    }
                    elseif ($facture['tva_taux'] == 0.196)
                    {
                        $compte = '707175';
                    } // 19.6%
                    else
                    {
                        $compte = '707150';
                    } // 20%
                }

                if ($facture['detaxe_export'] == 'oui')
                {
                    $mnt = round($facture['ecotaxe'] * (1 + $facture['tva_taux']), 2);
                    // export
                    if ($facture['cee'] == 'non' or $facture['dom-tom'] == 'DOM' or $facture['dom-tom'] == 'TOM'  || $facture['cee'] == 'oui' && empty($facture['intracomm']))
                    {
                        $compte = 707151;
                    }

                    // intra communautaire CEE
                    elseif ($facture['cee'] == 'oui')
                    {
                        $compte = '707152';
                    }
                }

                // intra groupe
                switch ($this->isIntraGroup($facture['no_client']))
                {
                    case 1:
                        $compte = '707154';
                        break;
                    case 2:
                        $compte = '707155';
                        break;
                    case 3:
                        $compte = '707156';
                        break;
                    case 4:
                        $compte = '707158';
                        break;
                    case 5:
                        $compte = '707157';
                        break;
                    case 6:
                        $compte = '707149';
                        break;
                }

                // Amazon UK
                if ($facture['cmdo'] == 1)
                {
                    $compte = '707153';
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $compte, $label, $mnt);
                $credit += $mnt;
            }

            // vente sorecop "sorecop privée"
            if ($facture['sorecop'] <> 0)
            {
                // FR par défaut
                $compte = '707160';
                $label  = 'COPIE PRIVEE FRANCE';
                $mnt    = round($facture['sorecop'] * $taux, 2);

                if (strtotime($facture['date_facture']) < strtotime('2021-10-01')){
                    if ($facture['pays'] === 'BEL' && $facture['tva_taux'] == 0.210) // Belgique 21%
                    {
                        $compte = '707169';
                    }
                    elseif ($facture['pays'] === 'DEU')
                    {
                        $compte = '707168';
                    }
                } else {
                    if (array_key_exists($facture['pays'], $compte_pays))
                    {
                        $compte = $compte_pays[$facture['pays']]['sorecop'];
                    }
                }

                // detaxe
                if ($facture['detaxe_export'] == 'oui')
                {
                    $mnt = round($facture['sorecop'] * (1 + $facture['tva_taux']), 2);
                    // export
                    if ($facture['cee'] == 'non' or $facture['dom-tom'] == 'DOM' or $facture['dom-tom'] == 'TOM' || $facture['cee'] == 'oui' && empty($facture['intracomm']))
                    {
                        $compte = 707161;
                    }
                    // intra communautaire CEE
                    elseif ($facture['cee'] == 'oui')
                    {
                        $compte = '707162';
                    }
                }

                // Intra groupe
                switch ($this->isIntraGroup($facture['no_client']))
                {
                    case 2:
                        $compte = '707163';
                        break;
                    case 5:
                        $compte = '707170';
                        break;
                    case 6:
                        $compte = '707164';
                        break;
                    default:
                        break;
                }

                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, $compte, $label, $mnt);
                $credit += $mnt;
            }

            // vente matériel
            $mnt = ($facture['cmdo'] == 1) ? round($facture['montant_TTC'] * $taux, 2) : $facture['montant_TTC'] * $taux;

            if ($this->isIntraGroup($facture['no_client']))
            {
                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, $no_compte_tiers, $lettre_debit, '411200', $facture['client'], $mnt);
            }
            else if ('entreprise' == $facture['type_client'])
            {
                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, $no_compte_tiers, $lettre_debit, '411100', $facture['client'], $mnt);
            }
            else
            {
                $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, $no_compte_tiers, $lettre_debit, '411000', $facture['client'], $mnt);
            }
            $debit = $mnt;

            settype($credit, 'float');
            settype($debit, 'float');
            $venteServ = $facture['vente_service'];

            if ($facture['vente_materiel'] <> 0)
            {
                // France
                if ($facture['detaxe_export'] == 'non')
                {

                    if (strtotime($facture['date_facture']) < strtotime('2021-10-01')){
                        if ($facture['pays'] === 'BEL' && $facture['tva_taux'] == 0.210) // Belgique 21%
                        {
                            $compte = '707121';
                        }
                        elseif ($facture['pays'] === 'DEU')
                        {
                            $compte = '707127';
                        }
                        elseif ($facture['tva_taux'] == 0.196)
                        {
                            $compte = '707125';
                        } // 19.6%
                        else
                        {
                            $compte = '707120';
                        } // 20%
                    } else {
                        if (array_key_exists($facture['pays'], $compte_pays))
                        {
                            $compte = $compte_pays[$facture['pays']]['vente_materiel'];
                        }
                        elseif ($facture['tva_taux'] == 0.196)
                        {
                            $compte = '707125';
                        } // 19.6%
                        else
                        {
                            $compte = '707120';
                        } // 20%
                    }

                    // Intra groupe
                    switch ($this->isIntraGroup($facture['no_client']))
                    {
                        case 1:
                            $compte = '707002';
                            break;
                        case 2:
                            $compte = '707003';
                            break;
                        case 3:
                            $compte = '707004';
                            break;
                        case 4:
                            $compte = '707006';
                            break;
                        case 5:
                            $compte = '707010';
                            break;
                        case 6:
                            $compte = '707012';
                            break;
                    }

                    // Amazon UK
                    if ($facture['cmdo'] == 1)
                    {
                        $compte = '707130';
                    }

                    $lc     = ($facture['vente_materiel'] < 0 ? ($lettre_credit == 'D' ? 'C' : 'D') : $lettre_credit);
                    $mnt    = $debit - $credit-$venteServ;
                    $credit = $debit;
                    $mnt    = ($mnt < 0) ? -$mnt : $mnt;
                    $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lc, $compte, 'VENTE MATERIEL HT', $mnt);
                }
                // detaxe intracomm
                elseif ($facture['cee'] == 'oui' && !empty($facture['intracomm']))
                {
                    $mnt    = $debit - $credit-$venteServ;
                    $credit = $debit;
                    switch ($this->isIntraGroup($facture['no_client'])) {
                        case 6:
                            $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, '707012', 'VENTES CILO', $mnt);
                            break;
                        default:
                            $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, '707220', 'LIVRAISONS INTRACOMMUNAUTAIRES', $mnt);
                    }
                }
                // detaxe export and more ...
                # elseif ($facture['cee'] == 'non' or $facture['dom-tom'] == 'DOM' or $facture['dom-tom'] == 'TOM') {
                else
                {
                    $mnt    = $debit - $credit-$venteServ;
                    $credit = $debit;
                    $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17), $lettre_credit, '707200', 'VENTES EXPORT', $mnt);
                }
            }
            if ($facture['vente_service'] <> 0)
            {
                if ($facture['montant_serv_dep_ht'] > 0)
                {
                    $mnt = $facture['montant_serv_dep_ht'];
                    $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17),$lettre_credit, '708240', 'FORFAIT DÉPLACEMENT', $mnt);
                }
                if ($facture['montant_serv_inst_ht'] > 0)
                {
                    $mnt = $facture['montant_serv_inst_ht'];
                    $this->output_data .= $this->formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, str_pad("", 17),$lettre_credit, '706100', 'INSTALLATION HOME-CINEMA/HIFI', $mnt);
                }
                $credit = $debit;
            }
            $diff = round($credit, 3) - round($debit, 3);

            if ($diff != 0)
            {
                throw new Exception(__CLASS__ . '  ' . __FUNCTION__ . 'probleme facture : ' . $facture['id_facture'] . ' -> erreur credit (' . $credit . ') != debit (' . $debit . ') ; diff : ' . $diff . '' . print_r($facture, true));
            }

        }

        $this->cree_output_fichier();
    }

    protected function envoie_output_fichier()
    {
        $fichier_nom = 'sage_factures_' . date('d_m_Y') . '.txt';
        header('Content-Type: application/force-download');
        header('Content-Disposition: attachment; filename="' . $fichier_nom . '"');
        header('Content-Length: ' . filesize($this->output_fichier));

        readfile($this->output_fichier);
        exit;
    }

    private function formate_ligne($journal, $date_ecriture, $numerocommande, $no_facture, $ref, $no_compte_tiers, $lettre_credit, $compte, $libelle, $mnt)
    {
        $compte         = str_pad($compte, 13);
        $libelle_compte = str_pad(substr($libelle, 0, 35), 35);
        $mnt            = str_pad(str_replace('.', ',', $mnt), 14, ' ', STR_PAD_LEFT);

        return $journal . $date_ecriture . $numerocommande . $compte . $no_facture . $ref . $no_compte_tiers . $libelle_compte . $lettre_credit . $mnt . "\r\n";

    }

    /**
     * Client Intra Group
     * return false when not
     *
     * @param int $customerId
     */
    private function isIntraGroup($customerId)
    {
        $return = false;
        // PhC
        if (in_array($customerId, array('0002183')))
        {
            $return = 1;
        }
        // Connecting Technology
        elseif (in_array($customerId, array('0289781', '0360068', '0363521')))
        {
            $return = 2;
        }
        // Connecting Store
        elseif (in_array($customerId, array(
            '0359176',
            '0367425',
            '0373944',
            '0398357',
            '0386866',
            '0473880',
            '0471918',
            '0583697',
            '0591141',
            '0592583',
            '0646508'
        )))
        {
            $return = 3;
        }
        // AV Industry
        elseif (in_array($customerId, array(
            '0275509',
            '0484820',
            '0563653',
            '0657748',
            '0692409',
            '0692409',
            '0692087',
            '0641466',
            '0692409',
            '0692087',
            '0641466'
        )))
        {
            $return = 4;
        }
        // Easylounge
        elseif (in_array($customerId, array('0314559')))
        {
            $return = 5;
        }
        // Cilo
        elseif (in_array($customerId, array('1518400')))
        {
            $return = 6;
        }

        return $return;
    }

}

?>

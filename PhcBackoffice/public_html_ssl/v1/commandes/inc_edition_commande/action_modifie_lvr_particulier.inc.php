<?php
if (isset ($_POST["id_commande"]) and isset ($_POST["semaphore"]) and isset ($_POST['lvr_particulier']) and preg_match("#^Y$|^N$#", $_POST['lvr_particulier']))
{
  $lvr_particulier = $_POST['lvr_particulier'];
  $no_mobile_lvr = $_POST['no_mobile_lvr'];
  $type_lvr = $_POST['type_lvr'];
  $id_commande = $_POST['id_commande'];
  $semaphore = $_POST['semaphore'];
}
else
{

  echo "Problème.";
  exit;
}
if ($lvr_particulier == 'Y')
{
  if ($type_lvr != 'particulier')
  {
    $_HTML["erreur"] = 'Le type de contact de livraison doit être "particulier"';
    return false;
  }
}

if (!isset ($_HTML))
{
  $_HTML = array ();
}

$maCommande = new Commande($id_commande);
if (!is_null($maCommande->obtient_msgErr()))
{
  $_HTML["erreur"] = nl2br(htmlentities($maCommande->obtient_msgErr()));
  return false;
}

if (!$maCommande->definit_lvr_particulier($lvr_particulier))
{
  $_HTML["erreur"] = nl2br(htmlentities($maCommande->obtient_msgErr()));
  return false;
}
if (!$maCommande->actualise_lvr_particulier())
{
  $_HTML["erreur"] = nl2br(htmlentities($maCommande->obtient_msgErr()));
  return false;
}

header("Location:./edition_commande.php?id_commande=" . $maCommande->obtient_id_commande());
?>
